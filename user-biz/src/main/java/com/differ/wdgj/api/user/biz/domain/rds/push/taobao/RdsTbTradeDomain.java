package com.differ.wdgj.api.user.biz.domain.rds.push.taobao;

import com.differ.wdgj.api.user.biz.domain.rds.push.taobao.data.refund.RdsTbRefundOutRequest;

import java.util.List;

/**
 * 淘宝Rds - 订单相关操作（jdp_tb_trade）
 *
 * <AUTHOR>
 * @date 2025/6/26 09:45
 */
public class RdsTbOrderDomain {
    //region 常量
    /**
     * 会员名
     */
    private final String memberName;
    //endregion

    //region 构造
    public RdsTbOrderDomain(String memberName) {
        this.memberName = memberName;
    }
    //endregion

    /**
     * 获取订单发货信息列表
     *
     * @param tIds 普通订单单号列表
     * @return 订单发货信息列表
     */
    public int getOrderSendInfos(List<Integer> tIds) {

    }
}
